'use client';
import { Button, Divider, Input, Select, SelectItem } from '@heroui/react'
import { getTranslation } from '@/lib/i18n';
import { useState } from 'react';
import InputWithCopy from './InputWithCopy';
import TimeZone from './TimeZone';

export default function TimestampToDate({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const [timestamp, setTimestamp] = useState(Math.floor(Date.now() / 1000).toString());
  const [unit, setUnit] = useState('Seconds');
  const [timezone, setTimezone] = useState('');
  const [convertedDate1, setConvertedDate1] = useState('');
  const [convertedDate2, setConvertedDate2] = useState('');
  const [convertedDate3, setConvertedDate3] = useState('');

  // 单位选项
  const units = [
    { key: 'Seconds', label: t('Seconds') },
    { key: 'Milliseconds', label: t('Milliseconds') },
  ];

  // 转换时间戳
  const convertTimestamp = () => {
    try {
      let timestampValue = parseFloat(timestamp);
      if (isNaN(timestampValue)) {
        setConvertedDate1('');
        setConvertedDate2('');
        setConvertedDate3('');
        return;
      }

      // 如果是秒，转换为毫秒
      if (unit === 'Seconds') {
        timestampValue = timestampValue * 1000;
      }

      const date = new Date(timestampValue);

      if (isNaN(date.getTime())) {
        setConvertedDate1('');
        setConvertedDate2('');
        setConvertedDate3('');
        return;
      }

      const formatter1 = new Intl.DateTimeFormat(locale, {
        timeZone: timezone,
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).format(date);

      // Format YYYY-MM-DD HH:mm:ss
      const formatter2 = new Intl.DateTimeFormat('en-US', {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).format(date).replace(/(\d+)\/(\d+)\/(\d+)/, '$3-$1-$2').replace(',', '');

      // Format DD/MM/YYYY HH:mm:ss
      const formatter3 = new Intl.DateTimeFormat('en-US', {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).format(date).replace(',', '');

      setConvertedDate1(formatter1);
      setConvertedDate2(formatter2);
      setConvertedDate3(formatter3);
    } catch (error) {
      setConvertedDate1('');
      setConvertedDate2('');
      setConvertedDate3('');
    }
  };

  return (
    <div className="section mt-10 border rounded-lg px-6 py-4">
      <h2 className="text-2xl font-bold mb-4">{t('Timestamp to Date')}</h2>

      {/* 输入区域 */}
      <div className="flex gap-2 flex-wrap mb-4 items-center">
        <Input
          type="text"
          placeholder={t('Enter timestamp')}
          value={timestamp}
          onValueChange={setTimestamp}
          className="w-[160px]"
          label={t('Timestamp')}
        />

        <Select
          selectedKeys={[unit]}
          onSelectionChange={(keys) => setUnit(Array.from(keys)[0])}
          className="w-[140px]"
          label={t('Unit')}
        >
          {units.map((unitOption) => (
            <SelectItem key={unitOption.key} value={unitOption.key}>
              {unitOption.label}
            </SelectItem>
          ))}
        </Select>

        <TimeZone
          locale={locale}
          value={timezone}
          onChange={setTimezone}
          className="w-[340px]"
        />

        <Button onPress={convertTimestamp} color="primary">
          {t('Convert')}
        </Button>
      </div>

      <Divider className='my-4'></Divider>

      {/* 输出区域 */}
      <div className="flex gap-2 flex-wrap items-center">
        <InputWithCopy locale={locale} text={convertedDate1}></InputWithCopy>
        <InputWithCopy locale={locale} text={convertedDate2}></InputWithCopy>
        <InputWithCopy locale={locale} text={convertedDate3}></InputWithCopy>
      </div>
    </div >
  );
}
