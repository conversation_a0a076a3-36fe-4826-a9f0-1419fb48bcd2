import { getTranslation } from '@/lib/i18n';
import { useState, useEffect } from 'react';
import { Button, Input, Select, SelectItem } from '@heroui/react';
import TimeZone from './TimeZone';
import InputWithCopy from './InputWithCopy';

export default function DateToTimestamp({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const units = [
    { key: 'Seconds', label: t('Seconds') },
    { key: 'Milliseconds', label: t('Milliseconds') },
  ];

  // 格式化当前时间为 YYYY-MM-DD hh:mm:ss 格式
  const getCurrentDateTime = (locale = 'en', timezone = 'UTC') => {
    const date = new Date();
    const formatted = new Intl.DateTimeFormat(locale, {
      timeZone: timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).format(date);

    // Convert from MM/DD/YYYY, HH:MM:SS format to YYYY-MM-DD HH:MM:SS
    // const parts = formatted.split(', ');
    // const datePart = parts[0]; // MM/DD/YYYY
    // const timePart = parts[1]; // HH:MM:SS
    // const [month, day, year] = datePart.split('/');
    // return `${year}-${month}-${day} ${timePart}`;
    return formatted;
  };

  const [datetime, setDatetime] = useState('');
  const [timezone, setTimezone] = useState('');
  const [unit, setUnit] = useState('Seconds');
  const [convertedTimestamp, setConvertedTimestamp] = useState('');

  // 设置默认时间为当前时间
  useEffect(() => {
    setDatetime(getCurrentDateTime());
  }, []);

  const convertTimestamp = () => {
    if (!datetime || !timezone) {
      setConvertedTimestamp('');
      return;
    }

    try {
      // 解析输入的日期时间字符串
      const dateTimeRegex = /^(\d{4})-(\d{2})-(\d{2})\s+(\d{2}):(\d{2}):(\d{2})$/;
      const match = datetime.match(dateTimeRegex);

      if (!match) {
        setConvertedTimestamp('Invalid date format');
        return;
      }

      const [, year, month, day, hours, minutes, seconds] = match;

      // 将输入的时间视为指定时区的本地时间
      // 构造一个临时的Date对象
      const inputDate = new Date(
        parseInt(year),
        parseInt(month) - 1,
        parseInt(day),
        parseInt(hours),
        parseInt(minutes),
        parseInt(seconds)
      );

      // 检查日期是否有效
      if (isNaN(inputDate.getTime())) {
        setConvertedTimestamp('Invalid date');
        return;
      }

      // 获取本地时区偏移量（分钟）
      const localOffset = inputDate.getTimezoneOffset();

      // 获取目标时区在同一时间的偏移量
      // 创建一个在目标时区的格式化器
      const targetFormatter = new Intl.DateTimeFormat('en', {
        timeZone: timezone,
        timeZoneName: 'longOffset'
      });

      const parts = targetFormatter.formatToParts(inputDate);
      const offsetPart = parts.find(part => part.type === 'timeZoneName');
      let targetOffsetMinutes = 0;

      if (offsetPart && offsetPart.value) {
        const offsetMatch = offsetPart.value.match(/GMT([+-])(\d{2}):(\d{2})/);
        if (offsetMatch) {
          const sign = offsetMatch[1] === '+' ? 1 : -1;
          const offsetHours = parseInt(offsetMatch[2]);
          const offsetMinutes = parseInt(offsetMatch[3]);
          targetOffsetMinutes = sign * (offsetHours * 60 + offsetMinutes);
        }
      }

      // 计算时间戳
      // 输入时间被视为目标时区的本地时间
      // 需要转换为UTC时间戳
      const utcTimestamp = inputDate.getTime() + (localOffset * 60 * 1000) - (targetOffsetMinutes * 60 * 1000);

      // 根据单位转换
      let result;
      if (unit === 'Seconds') {
        result = Math.floor(utcTimestamp / 1000).toString();
      } else {
        result = utcTimestamp.toString();
      }

      setConvertedTimestamp(result);
    } catch (error) {
      console.error('Error converting timestamp:', error);
      setConvertedTimestamp('Conversion error');
    }
  };

  return (
    <div className="section mt-10 border rounded-lg px-6 py-4">
      <h2 className="text-2xl font-bold mb-4">{t('Date to Timestamp')}</h2>
      <div className='flex gap-2 items-center flex-wrap'>
        <Input
          type="text"
          placeholder={t('YYYY-MM-DD hh:mm:ss')}
          value={datetime}
          onValueChange={setDatetime}
          className="w-[160px]"
          label={t('Date')}
        />
        <TimeZone
          locale={locale}
          timezone={timezone}
          onChange={setTimezone}
        ></TimeZone>
        <Select
          selectedKeys={[unit]}
          onSelectionChange={(keys) => setUnit(Array.from(keys)[0])}
          className="w-[140px]"
          label={t('Unit')}
        >
          {units.map((unitOption) => (
            <SelectItem key={unitOption.key} value={unitOption.key}>
              {unitOption.label}
            </SelectItem>
          ))}
        </Select>
        <Button onPress={convertTimestamp} color="primary">
          {t('Convert')}
        </Button>
      </div>
      <InputWithCopy locale={locale} text={convertedTimestamp} className="mt-4"></InputWithCopy>
    </div>
  );
}
